#!/usr/bin/env python3
"""
小规模 cheetah_run 训练测试脚本
用于验证配置、checkpoint、日志功能是否正常
"""

import os
import sys
import argparse
import tempfile
import shutil

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import tensorflow as tf
from planet.scripts import train
from planet import tools

def create_test_config():
    """创建测试用的小规模配置"""
    return {
        'tasks': ['cheetah_run'],
        # 大幅减少训练规模以便快速测试
        'train_steps': 50,           # 默认50000，减少到50
        'test_steps': 10,            # 默认50，减少到10
        'max_steps': 200,            # 默认5e7，减少到200
        'num_seed_episodes': 2,      # 默认5，减少到2
        'collect_every': 30,         # 默认5000，减少到30
        'checkpoint_every': 20,      # 默认500，减少到20
        
        # 减少模型复杂度
        'model_size': 50,            # 默认200，减少到50
        'state_size': 10,            # 默认30，减少到10
        'num_layers': 2,             # 默认3，减少到2
        'num_units': 100,            # 默认300，减少到100
        'batch_shape': [10, 20],     # 默认[50, 50]，减少到[10, 20]
        
        # 减少规划复杂度
        'planner_amount': 100,       # 默认1000，减少到100
        'planner_iterations': 3,     # 默认10，减少到3
        'planner_topk': 20,          # 默认100，减少到20
        'planner_horizon': 6,        # 默认12，减少到6
        
        # 其他设置
        'action_repeat': 4,          # cheetah_run 默认值
        'isolate_envs': 'thread',    # 使用线程而非进程，更轻量
    }

def test_training(logdir, resume=False):
    """执行测试训练"""
    print(f"开始测试训练，日志目录: {logdir}")
    print(f"恢复训练: {resume}")
    
    # 创建参数
    config_params = create_test_config()
    
    # 模拟命令行参数
    class Args:
        def __init__(self):
            self.logdir = logdir
            self.num_runs = 1
            self.config = 'default'
            self.params = tools.AttrDict(config_params)
            self.ping_every = 0
            self.resume_runs = resume
    
    args = Args()
    
    try:
        # 执行训练
        train.main(args)
        print("训练完成！")
        return True
    except Exception as e:
        print(f"训练过程中出现错误: {e}")
        return False

def check_outputs(logdir):
    """检查输出文件"""
    print(f"\n检查输出文件 in {logdir}:")
    
    # 检查配置文件
    config_file = os.path.join(logdir, 'config.yaml')
    if os.path.exists(config_file):
        print("✓ 配置文件已保存: config.yaml")
        with open(config_file, 'r') as f:
            print(f"  配置文件大小: {len(f.read())} 字符")
    else:
        print("✗ 配置文件未找到")
    
    # 检查checkpoint文件
    checkpoint_files = []
    for root, dirs, files in os.walk(logdir):
        for file in files:
            if 'model.ckpt' in file:
                checkpoint_files.append(os.path.join(root, file))
    
    if checkpoint_files:
        print(f"✓ 找到 {len(checkpoint_files)} 个checkpoint文件")
        for ckpt in checkpoint_files[:3]:  # 只显示前3个
            print(f"  {ckpt}")
    else:
        print("✗ 未找到checkpoint文件")
    
    # 检查训练数据目录
    train_dir = os.path.join(logdir, 'train_episodes')
    test_dir = os.path.join(logdir, 'test_episodes')
    
    if os.path.exists(train_dir):
        train_files = len([f for f in os.listdir(train_dir) if f.endswith('.npz')])
        print(f"✓ 训练数据目录存在，包含 {train_files} 个episode文件")
    else:
        print("✗ 训练数据目录不存在")
    
    if os.path.exists(test_dir):
        test_files = len([f for f in os.listdir(test_dir) if f.endswith('.npz')])
        print(f"✓ 测试数据目录存在，包含 {test_files} 个episode文件")
    else:
        print("✗ 测试数据目录不存在")
    
    # 检查TensorBoard日志
    tb_dirs = []
    for root, dirs, files in os.walk(logdir):
        for file in files:
            if file.startswith('events.out.tfevents'):
                tb_dirs.append(root)
                break
    
    if tb_dirs:
        print(f"✓ 找到 {len(set(tb_dirs))} 个TensorBoard日志目录")
        for tb_dir in set(tb_dirs)[:3]:  # 只显示前3个
            print(f"  {tb_dir}")
    else:
        print("✗ 未找到TensorBoard日志文件")

def main():
    parser = argparse.ArgumentParser(description='PlaNet cheetah_run 小规模训练测试')
    parser.add_argument('--logdir', type=str, default=None,
                       help='日志目录路径，如果不指定则使用临时目录')
    parser.add_argument('--resume', action='store_true',
                       help='是否测试恢复训练功能')
    parser.add_argument('--keep-logs', action='store_true',
                       help='是否保留日志文件（默认会删除临时目录）')
    
    args = parser.parse_args()
    
    # 确定日志目录
    if args.logdir:
        logdir = os.path.expanduser(args.logdir)
        temp_dir = False
    else:
        logdir = tempfile.mkdtemp(prefix='planet_test_')
        temp_dir = True
        print(f"使用临时目录: {logdir}")
    
    try:
        # 第一次训练
        print("=" * 60)
        print("第一次训练测试")
        print("=" * 60)
        success1 = test_training(logdir, resume=False)
        
        if success1:
            print("\n第一次训练成功！")
            check_outputs(logdir)
            
            if args.resume:
                # 测试恢复训练
                print("\n" + "=" * 60)
                print("恢复训练测试")
                print("=" * 60)
                success2 = test_training(logdir, resume=True)
                
                if success2:
                    print("\n恢复训练成功！")
                    check_outputs(logdir)
                else:
                    print("\n恢复训练失败！")
        else:
            print("\n第一次训练失败！")
    
    finally:
        # 清理临时目录
        if temp_dir and not args.keep_logs:
            print(f"\n清理临时目录: {logdir}")
            shutil.rmtree(logdir, ignore_errors=True)
        elif temp_dir and args.keep_logs:
            print(f"\n保留日志目录: {logdir}")

if __name__ == '__main__':
    main()
