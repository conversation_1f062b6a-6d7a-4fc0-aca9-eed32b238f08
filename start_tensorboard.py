#!/usr/bin/env python3
"""
TensorBoard 启动脚本
自动查找训练日志并启动TensorBoard服务
"""

import os
import sys
import argparse
import subprocess
import glob
from datetime import datetime

def find_log_directories():
    """查找所有可用的日志目录"""
    log_dirs = []
    
    # 查找logs目录下的所有训练目录
    if os.path.exists('./logs'):
        for item in os.listdir('./logs'):
            item_path = os.path.join('./logs', item)
            if os.path.isdir(item_path):
                # 检查是否包含训练日志
                has_logs = False
                for root, dirs, files in os.walk(item_path):
                    if any(f.startswith('events.out.tfevents') for f in files):
                        has_logs = True
                        break
                
                if has_logs:
                    # 获取目录信息
                    stat = os.stat(item_path)
                    mtime = datetime.fromtimestamp(stat.st_mtime)
                    log_dirs.append({
                        'name': item,
                        'path': item_path,
                        'mtime': mtime,
                        'size': get_dir_size(item_path)
                    })
    
    # 按修改时间排序（最新的在前）
    log_dirs.sort(key=lambda x: x['mtime'], reverse=True)
    return log_dirs

def get_dir_size(path):
    """获取目录大小（MB）"""
    total_size = 0
    try:
        for dirpath, dirnames, filenames in os.walk(path):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                if os.path.exists(filepath):
                    total_size += os.path.getsize(filepath)
    except:
        pass
    return total_size / (1024 * 1024)  # 转换为MB

def show_available_logs():
    """显示可用的日志目录"""
    log_dirs = find_log_directories()
    
    if not log_dirs:
        print("未找到任何训练日志目录")
        return []
    
    print("可用的训练日志目录:")
    print("="*80)
    print(f"{'序号':<4} {'目录名':<30} {'修改时间':<20} {'大小(MB)':<10}")
    print("-"*80)
    
    for i, log_dir in enumerate(log_dirs, 1):
        print(f"{i:<4} {log_dir['name']:<30} {log_dir['mtime'].strftime('%Y-%m-%d %H:%M'):<20} {log_dir['size']:<10.1f}")
    
    return log_dirs

def start_tensorboard(logdir, port=6006, host='0.0.0.0'):
    """启动TensorBoard"""
    print(f"\n启动TensorBoard...")
    print(f"日志目录: {logdir}")
    print(f"服务地址: http://{host}:{port}")
    print(f"本地访问: http://localhost:{port} (需要SSH隧道)")
    print("\n" + "="*60)
    print("SSH隧道命令:")
    print(f"ssh -L {port}:localhost:{port} xieting@*************")
    print("="*60)
    
    # 构建命令
    cmd = [
        'tensorboard',
        '--logdir', logdir,
        '--host', host,
        '--port', str(port),
        '--reload_interval', '30'  # 30秒刷新一次
    ]
    
    try:
        print(f"\n执行命令: {' '.join(cmd)}")
        print("\n按 Ctrl+C 停止TensorBoard服务\n")
        
        # 启动TensorBoard
        subprocess.run(cmd)
        
    except KeyboardInterrupt:
        print("\nTensorBoard服务已停止")
    except FileNotFoundError:
        print("错误: 未找到tensorboard命令")
        print("请确保已安装TensorBoard: pip install tensorboard")
    except Exception as e:
        print(f"启动TensorBoard时出错: {e}")

def main():
    parser = argparse.ArgumentParser(description='TensorBoard 启动器')
    parser.add_argument('--logdir', type=str, default=None,
                       help='指定日志目录路径')
    parser.add_argument('--port', type=int, default=6006,
                       help='TensorBoard端口 (默认: 6006)')
    parser.add_argument('--host', type=str, default='0.0.0.0',
                       help='绑定主机地址 (默认: 0.0.0.0)')
    parser.add_argument('--list', action='store_true',
                       help='列出所有可用的日志目录')
    parser.add_argument('--latest', action='store_true',
                       help='使用最新的日志目录')
    parser.add_argument('--all', action='store_true',
                       help='查看所有日志目录')
    
    args = parser.parse_args()
    
    print("PlaNet TensorBoard 启动器")
    print("="*60)
    
    # 如果只是列出目录
    if args.list:
        show_available_logs()
        return
    
    # 确定日志目录
    logdir = None
    
    if args.logdir:
        # 用户指定目录
        logdir = os.path.expanduser(args.logdir)
        if not os.path.exists(logdir):
            print(f"错误: 指定的目录不存在: {logdir}")
            return
    elif args.all:
        # 查看所有日志
        logdir = './logs'
        if not os.path.exists(logdir):
            print("错误: logs目录不存在")
            return
    elif args.latest:
        # 使用最新的日志目录
        log_dirs = find_log_directories()
        if not log_dirs:
            print("错误: 未找到任何训练日志")
            return
        logdir = log_dirs[0]['path']
        print(f"使用最新的日志目录: {log_dirs[0]['name']}")
    else:
        # 交互式选择
        log_dirs = show_available_logs()
        if not log_dirs:
            return
        
        try:
            print(f"\n请选择要查看的日志目录 (1-{len(log_dirs)}):")
            print("输入 'a' 查看所有日志目录")
            choice = input("选择: ").strip()
            
            if choice.lower() == 'a':
                logdir = './logs'
            else:
                choice_num = int(choice)
                if 1 <= choice_num <= len(log_dirs):
                    logdir = log_dirs[choice_num - 1]['path']
                else:
                    print("无效的选择")
                    return
        except (ValueError, KeyboardInterrupt):
            print("\n已取消")
            return
    
    # 启动TensorBoard
    start_tensorboard(logdir, args.port, args.host)

if __name__ == '__main__':
    main()
