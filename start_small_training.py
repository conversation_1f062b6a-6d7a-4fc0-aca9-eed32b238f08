#!/usr/bin/env python3
"""
PlaNet cheetah_run 小规模训练启动脚本
用于快速验证和测试训练流程
"""

import os
import sys
import argparse
import subprocess
from datetime import datetime

def create_training_configs():
    """创建不同规模的训练配置"""
    configs = {
        'tiny': {
            'description': '极小规模 - 快速测试（约2-3分钟）',
            'params': {
                'tasks': ['cheetah_run'],
                'train_steps': 5,
                'test_steps': 2,
                'max_steps': 20,
                'num_seed_episodes': 1,
                'collect_every': 10,
                'checkpoint_every': 10,
                'model_size': 20,
                'state_size': 5,
                'num_layers': 1,
                'num_units': 50,
                'batch_shape': [3, 8],
                'planner_amount': 50,
                'planner_iterations': 2,
                'planner_topk': 10,
            }
        },
        'small': {
            'description': '小规模 - 基础训练（约10-15分钟）',
            'params': {
                'tasks': ['cheetah_run'],
                'train_steps': 50,
                'test_steps': 10,
                'max_steps': 200,
                'num_seed_episodes': 2,
                'collect_every': 30,
                'checkpoint_every': 25,
                'model_size': 50,
                'state_size': 10,
                'num_layers': 2,
                'num_units': 100,
                'batch_shape': [10, 20],
                'planner_amount': 200,
                'planner_iterations': 5,
                'planner_topk': 30,
            }
        },
        'medium': {
            'description': '中等规模 - 完整测试（约30-60分钟）',
            'params': {
                'tasks': ['cheetah_run'],
                'train_steps': 200,
                'test_steps': 20,
                'max_steps': 1000,
                'num_seed_episodes': 3,
                'collect_every': 100,
                'checkpoint_every': 50,
                'model_size': 100,
                'state_size': 20,
                'num_layers': 2,
                'num_units': 200,
                'batch_shape': [20, 30],
                'planner_amount': 500,
                'planner_iterations': 8,
                'planner_topk': 50,
            }
        }
    }
    return configs

def run_training(logdir, config_name, scale='small', resume=False):
    """运行训练"""
    configs = create_training_configs()
    
    if scale not in configs:
        print(f"错误：未知的规模 '{scale}'")
        print(f"可用规模: {list(configs.keys())}")
        return False
    
    config = configs[scale]
    print(f"开始 {scale} 规模训练")
    print(f"描述: {config['description']}")
    print(f"日志目录: {logdir}")
    print(f"配置: {config_name}")
    print(f"恢复训练: {resume}")
    
    # 构建参数字符串
    params_str = str(config['params']).replace("'", '"')
    
    # 构建命令
    cmd = [
        'python3', '-m', 'planet.scripts.train',
        '--logdir', logdir,
        '--config', config_name,
        '--params', params_str,
        '--resume_runs', str(resume)
    ]
    
    print("\n执行命令:")
    print(' '.join(cmd))
    print("\n" + "="*60)
    
    # 设置环境变量
    env = os.environ.copy()
    env['MUJOCO_GL'] = 'egl'
    
    try:
        # 实时输出训练过程
        process = subprocess.Popen(cmd, env=env, stdout=subprocess.PIPE, 
                                 stderr=subprocess.STDOUT, text=True, bufsize=1)
        
        for line in process.stdout:
            print(line.rstrip())
        
        process.wait()
        
        if process.returncode == 0:
            print("\n" + "="*60)
            print("✓ 训练成功完成！")
            return True
        else:
            print("\n" + "="*60)
            print(f"✗ 训练失败，返回码: {process.returncode}")
            return False
            
    except KeyboardInterrupt:
        print("\n训练被用户中断")
        process.terminate()
        return False
    except Exception as e:
        print(f"\n训练异常: {e}")
        return False

def show_configs():
    """显示可用的配置"""
    configs = create_training_configs()
    print("可用的训练规模配置:")
    print("="*60)
    
    for scale, config in configs.items():
        print(f"\n{scale.upper()}:")
        print(f"  描述: {config['description']}")
        print(f"  训练步数: {config['params']['train_steps']}")
        print(f"  最大步数: {config['params']['max_steps']}")
        print(f"  批次大小: {config['params']['batch_shape']}")
        print(f"  模型大小: {config['params']['model_size']}")

def main():
    parser = argparse.ArgumentParser(description='PlaNet cheetah_run 小规模训练启动器')
    parser.add_argument('--scale', type=str, default='small', 
                       choices=['tiny', 'small', 'medium'],
                       help='训练规模 (默认: small)')
    parser.add_argument('--logdir', type=str, default=None,
                       help='日志目录路径，如果不指定则自动生成')
    parser.add_argument('--config', type=str, default='debug',
                       choices=['debug', 'default'],
                       help='基础配置 (默认: debug)')
    parser.add_argument('--resume', action='store_true',
                       help='恢复已有的训练')
    parser.add_argument('--show-configs', action='store_true',
                       help='显示可用的配置并退出')
    
    args = parser.parse_args()
    
    if args.show_configs:
        show_configs()
        return
    
    # 确定日志目录
    if args.logdir:
        logdir = os.path.expanduser(args.logdir)
    else:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        logdir = f"./logs/cheetah_{args.scale}_{timestamp}"
    
    print("PlaNet cheetah_run 训练启动器")
    print("="*60)
    
    # 运行训练
    success = run_training(logdir, args.config, args.scale, args.resume)
    
    if success:
        print(f"\n训练日志保存在: {logdir}")
        print(f"查看TensorBoard: tensorboard --logdir {logdir}")
        
        # 检查输出文件
        run_dirs = [d for d in os.listdir(logdir) if os.path.isdir(os.path.join(logdir, d)) and d.isdigit()]
        if run_dirs:
            run_dir = os.path.join(logdir, run_dirs[0])
            
            # 统计文件
            train_episodes = len([f for f in os.listdir(os.path.join(run_dir, 'train_episodes')) if f.endswith('.npz')])
            test_episodes = len([f for f in os.listdir(os.path.join(run_dir, 'test_episodes')) if f.endswith('.npz')])
            checkpoints = len([f for f in os.listdir(run_dir) if 'model.ckpt' in f])
            
            print(f"\n生成文件统计:")
            print(f"  训练episodes: {train_episodes}")
            print(f"  测试episodes: {test_episodes}")
            print(f"  Checkpoint文件: {checkpoints}")
    else:
        print(f"\n训练失败，请检查日志: {logdir}")

if __name__ == '__main__':
    main()
