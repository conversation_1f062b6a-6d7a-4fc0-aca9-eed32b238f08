Traceback (most recent call last):
  File "/home/<USER>/planet-master/planet/training/running.py", line 201, in __iter__
    for value in self._process_fn(self._logdir, *args):
  File "/home/<USER>/planet-master/planet/scripts/train.py", line 71, in process
    preprocess_fn=config.preprocess_fn)
  File "/home/<USER>/planet-master/planet/tools/numpy_episodes.py", line 51, in numpy_episodes
    dtypes, shapes = _read_spec(reader, train_dir)
  File "/home/<USER>/planet-master/planet/tools/numpy_episodes.py", line 149, in _read_spec
    episode = next(episodes)
  File "/home/<USER>/planet-master/planet/tools/numpy_episodes.py", line 110, in reload_loader
    yield reader(filename)
  File "/home/<USER>/planet-master/planet/tools/numpy_episodes.py", line 132, in episode_reader
    episode = {key: _convert_type(episode[key]) for key in episode.keys()}
  File "/home/<USER>/planet-master/planet/tools/numpy_episodes.py", line 132, in <dictcomp>
    episode = {key: _convert_type(episode[key]) for key in episode.keys()}
  File "/home/<USER>/anaconda3/envs/planet/lib/python3.7/site-packages/numpy/lib/npyio.py", line 249, in __getitem__
    bytes = self.zip.open(key)
  File "/home/<USER>/anaconda3/envs/planet/lib/python3.7/zipfile.py", line 1516, in open
    self._fpclose, self._lock, lambda: self._writing)
  File "/home/<USER>/anaconda3/envs/planet/lib/python3.7/zipfile.py", line 734, in __init__
    self.seekable = file.seekable
AttributeError: 'GFile' object has no attribute 'seekable'

