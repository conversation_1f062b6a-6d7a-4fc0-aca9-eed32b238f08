#!/usr/bin/env python3
"""
验证 PlaNet cheetah_run 训练的完整脚本
检查配置、checkpoint、日志、恢复训练等功能
"""

import os
import sys
import argparse
import subprocess
import numpy as np

def check_training_outputs(logdir):
    """检查训练输出文件"""
    print(f"\n检查训练输出 in {logdir}:")

    results = {}

    # 查找实际的运行目录（通常是 00001）
    run_dirs = [d for d in os.listdir(logdir) if os.path.isdir(os.path.join(logdir, d)) and d.isdigit()]
    if not run_dirs:
        print("✗ 未找到运行目录")
        return {key: False for key in ['config', 'train_episodes', 'test_episodes', 'tensorboard', 'completion']}

    run_dir = os.path.join(logdir, run_dirs[0])  # 使用第一个运行目录
    print(f"检查运行目录: {run_dirs[0]}")

    # 检查配置文件
    config_file = os.path.join(run_dir, 'config.yaml')
    if os.path.exists(config_file):
        print("✓ 配置文件已保存: config.yaml")
        with open(config_file, 'r') as f:
            config_content = f.read()
            print(f"  配置文件大小: {len(config_content)} 字符")
            results['config'] = True
    else:
        print("✗ 配置文件未找到")
        results['config'] = False
    
    # 检查训练数据目录
    train_dir = os.path.join(run_dir, 'train_episodes')
    test_dir = os.path.join(run_dir, 'test_episodes')
    
    if os.path.exists(train_dir):
        train_files = [f for f in os.listdir(train_dir) if f.endswith('.npz')]
        print(f"✓ 训练数据目录存在，包含 {len(train_files)} 个episode文件")
        results['train_episodes'] = len(train_files) > 0
        
        # 检查episode文件内容
        if train_files:
            sample_file = os.path.join(train_dir, train_files[0])
            try:
                data = np.load(sample_file)
                print(f"  样本episode包含键: {list(data.keys())}")
                if 'image' in data:
                    print(f"  图像形状: {data['image'].shape}")
                if 'reward' in data:
                    print(f"  奖励数量: {len(data['reward'])}")
            except Exception as e:
                print(f"  读取episode文件失败: {e}")
    else:
        print("✗ 训练数据目录不存在")
        results['train_episodes'] = False
    
    if os.path.exists(test_dir):
        test_files = [f for f in os.listdir(test_dir) if f.endswith('.npz')]
        print(f"✓ 测试数据目录存在，包含 {len(test_files)} 个episode文件")
        results['test_episodes'] = len(test_files) > 0
    else:
        print("✗ 测试数据目录不存在")
        results['test_episodes'] = False
    
    # 检查TensorBoard日志
    tb_dirs = []
    for root, dirs, files in os.walk(run_dir):
        for file in files:
            if file.startswith('events.out.tfevents'):
                tb_dirs.append(root)
                break

    if tb_dirs:
        print(f"✓ 找到 {len(set(tb_dirs))} 个TensorBoard日志目录")
        for tb_dir in set(tb_dirs):
            rel_path = os.path.relpath(tb_dir, run_dir)
            print(f"  {rel_path}")
        results['tensorboard'] = True
    else:
        print("✗ 未找到TensorBoard日志文件")
        results['tensorboard'] = False

    # 检查checkpoint文件
    checkpoint_files = []
    for file in os.listdir(run_dir):
        if 'model.ckpt' in file:
            checkpoint_files.append(file)

    if checkpoint_files:
        print(f"✓ 找到 {len(checkpoint_files)} 个checkpoint文件")
        results['checkpoints'] = True
    else:
        print("✗ 未找到checkpoint文件")
        results['checkpoints'] = False

    # 检查状态文件
    done_file = os.path.join(run_dir, 'DONE')
    if os.path.exists(done_file):
        print("✓ 训练完成标记文件存在")
        results['completion'] = True
    else:
        print("✗ 训练完成标记文件不存在")
        results['completion'] = False
    
    return results

def run_training(logdir, config_name='debug', extra_params=None):
    """运行训练"""
    print(f"\n开始训练，日志目录: {logdir}")
    print(f"配置: {config_name}")
    
    # 基础参数
    base_params = {
        'tasks': ['cheetah_run'],
        'train_steps': 10,
        'test_steps': 5,
        'max_steps': 50,
        'num_seed_episodes': 1,
        'collect_every': 15,
        'checkpoint_every': 10,
    }
    
    # 合并额外参数
    if extra_params:
        base_params.update(extra_params)
    
    # 构建参数字符串
    params_str = str(base_params).replace("'", '"')
    
    # 构建命令
    cmd = [
        'python3', '-m', 'planet.scripts.train',
        '--logdir', logdir,
        '--config', config_name,
        '--params', params_str
    ]
    
    print("执行命令:", ' '.join(cmd))
    
    # 设置环境变量
    env = os.environ.copy()
    env['MUJOCO_GL'] = 'egl'
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, 
                              timeout=600, env=env)
        
        if result.returncode == 0:
            print("✓ 训练成功完成")
            return True
        else:
            print(f"✗ 训练失败，返回码: {result.returncode}")
            print("错误输出:", result.stderr[-1000:])  # 只显示最后1000字符
            return False
            
    except subprocess.TimeoutExpired:
        print("✗ 训练超时")
        return False
    except Exception as e:
        print(f"✗ 训练异常: {e}")
        return False

def test_resume_training(logdir):
    """测试恢复训练"""
    print(f"\n测试恢复训练功能...")
    
    # 运行额外的训练步骤
    extra_params = {
        'train_steps': 5,  # 额外的训练步骤
        'test_steps': 2,
        'max_steps': 30,
    }
    
    cmd = [
        'python3', '-m', 'planet.scripts.train',
        '--logdir', logdir,
        '--config', 'debug',
        '--resume_runs', 'True',
        '--params', str(extra_params).replace("'", '"')
    ]
    
    env = os.environ.copy()
    env['MUJOCO_GL'] = 'egl'
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, 
                              timeout=300, env=env)
        
        if result.returncode == 0:
            print("✓ 恢复训练成功")
            return True
        else:
            print(f"✗ 恢复训练失败，返回码: {result.returncode}")
            print("错误输出:", result.stderr[-500:])
            return False
            
    except Exception as e:
        print(f"✗ 恢复训练异常: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='验证 PlaNet 训练功能')
    parser.add_argument('--logdir', type=str, default='./logs/validation_test',
                       help='日志目录路径')
    parser.add_argument('--test-resume', action='store_true',
                       help='是否测试恢复训练功能')
    parser.add_argument('--clean', action='store_true',
                       help='训练前清理日志目录')
    
    args = parser.parse_args()
    
    logdir = os.path.expanduser(args.logdir)
    
    # 清理目录
    if args.clean and os.path.exists(logdir):
        import shutil
        print(f"清理目录: {logdir}")
        shutil.rmtree(logdir)
    
    print("PlaNet 训练验证")
    print("=" * 60)
    
    # 第一次训练
    print("第一阶段：初始训练")
    print("-" * 40)
    success1 = run_training(logdir)
    
    if success1:
        print("\n初始训练成功！")
        results1 = check_training_outputs(logdir)
        
        if args.test_resume:
            # 测试恢复训练
            print("\n第二阶段：恢复训练测试")
            print("-" * 40)
            success2 = test_resume_training(logdir)
            
            if success2:
                print("\n恢复训练成功！")
                results2 = check_training_outputs(logdir)
            else:
                print("\n恢复训练失败！")
                results2 = {}
        else:
            results2 = {}
    else:
        print("\n初始训练失败！")
        results1 = {}
        results2 = {}
    
    # 总结
    print("\n" + "=" * 60)
    print("验证结果总结:")
    
    all_checks = [
        ("配置文件保存", results1.get('config', False)),
        ("训练数据收集", results1.get('train_episodes', False)),
        ("测试数据收集", results1.get('test_episodes', False)),
        ("TensorBoard日志", results1.get('tensorboard', False)),
        ("Checkpoint文件", results1.get('checkpoints', False)),
        ("训练完成标记", results1.get('completion', False)),
    ]
    
    if args.test_resume:
        all_checks.append(("恢复训练功能", success2 if 'success2' in locals() else False))
    
    passed = 0
    for check_name, success in all_checks:
        status = "✓ 通过" if success else "✗ 失败"
        print(f"  {check_name}: {status}")
        if success:
            passed += 1
    
    print(f"\n总计: {passed}/{len(all_checks)} 检查通过")
    
    if passed == len(all_checks):
        print("🎉 所有验证通过！PlaNet 训练系统工作正常。")
        print(f"\n可以使用以下命令查看TensorBoard日志:")
        print(f"tensorboard --logdir {logdir}")
    else:
        print("⚠️  部分验证失败，请检查配置。")
    
    print(f"\n日志目录: {logdir}")

if __name__ == '__main__':
    main()
