# TensorBoard 查看指南

本指南介绍如何查看 PlaNet 训练的 TensorBoard 日志。

## 快速开始

### 方法1: 使用便捷脚本（推荐）

```bash
# 查看所有可用的训练日志
python3 start_tensorboard.py --list

# 交互式选择日志目录
python3 start_tensorboard.py

# 自动使用最新的训练日志
python3 start_tensorboard.py --latest

# 查看所有训练日志
python3 start_tensorboard.py --all

# 指定特定目录
python3 start_tensorboard.py --logdir ./logs/validation_test
```

### 方法2: 直接使用TensorBoard命令

```bash
# 查看特定训练
tensorboard --logdir ./logs/validation_test --host 0.0.0.0 --port 6006

# 查看所有训练
tensorboard --logdir ./logs --host 0.0.0.0 --port 6006
```

## SSH隧道设置

### 1. 在本地机器上建立SSH隧道

```bash
ssh -L 6006:localhost:6006 xieting@*************
```

### 2. 在浏览器中访问

打开浏览器，访问：
```
http://localhost:6006
```

## TensorBoard界面说明

### 主要标签页

1. **SCALARS（标量）**
   - `objectives/reward`: 奖励损失
   - `objectives/divergence`: KL散度损失  
   - `objectives/image`: 图像重建损失
   - `grad_norms/main`: 梯度范数

2. **IMAGES（图像）**
   - `image_summary`: 原始观察图像
   - `image_pred`: 模型预测图像
   - `image_posterior_sample`: 后验采样图像

3. **HISTOGRAMS（直方图）**
   - 模型参数分布
   - 梯度分布

4. **GRAPHS（计算图）**
   - TensorFlow计算图结构

### 重要指标解读

#### 训练指标
- **reward**: 奖励预测损失，越低越好
- **divergence**: 状态分布的KL散度，控制正则化
- **image**: 图像重建损失，越低表示重建质量越好
- **grad_norms**: 梯度范数，监控训练稳定性

#### 测试指标
- **Score**: 测试episode的实际得分，越高越好
- 测试时的各项损失应该与训练时保持一致

## 常用操作

### 1. 比较多次训练

```bash
# 同时查看多个训练目录
tensorboard --logdir name1:./logs/run1,name2:./logs/run2 --host 0.0.0.0 --port 6006
```

### 2. 实时监控

TensorBoard会自动刷新，无需重启服务。训练过程中可以实时查看进度。

### 3. 下载数据

在TensorBoard界面中，可以下载标量数据为CSV格式进行进一步分析。

## 故障排除

### 1. 端口被占用

```bash
# 使用不同端口
python3 start_tensorboard.py --port 6007

# 对应的SSH隧道
ssh -L 6007:localhost:6007 xieting@*************
```

### 2. 无法访问

检查：
- SSH隧道是否正常建立
- TensorBoard服务是否正在运行
- 防火墙设置

### 3. 日志文件损坏

```bash
# 清理损坏的日志文件
find ./logs -name "events.out.tfevents*" -size 0 -delete
```

## 高级用法

### 1. 自定义端口和主机

```bash
python3 start_tensorboard.py --host 127.0.0.1 --port 8080
```

### 2. 多用户访问

```bash
# 绑定到所有网络接口（注意安全性）
python3 start_tensorboard.py --host 0.0.0.0 --port 6006
```

### 3. 后台运行

```bash
# 使用nohup在后台运行
nohup python3 start_tensorboard.py --latest > tensorboard.log 2>&1 &

# 查看进程
ps aux | grep tensorboard

# 停止服务
pkill -f tensorboard
```

## 示例：完整的查看流程

### 1. 运行训练
```bash
python3 start_small_training.py --scale small
```

### 2. 启动TensorBoard
```bash
python3 start_tensorboard.py --latest
```

### 3. 建立SSH隧道（在本地机器）
```bash
ssh -L 6006:localhost:6006 xieting@*************
```

### 4. 在浏览器中查看
访问 `http://localhost:6006`

## 有用的快捷命令

创建一个别名方便使用：

```bash
# 添加到 ~/.bashrc
alias tb='python3 /home/<USER>/planet-master/start_tensorboard.py'
alias tb-latest='python3 /home/<USER>/planet-master/start_tensorboard.py --latest'
alias tb-list='python3 /home/<USER>/planet-master/start_tensorboard.py --list'

# 重新加载配置
source ~/.bashrc

# 使用别名
tb-list
tb-latest
```

## 注意事项

1. **资源使用**: TensorBoard会占用一定的内存和CPU资源
2. **网络安全**: 避免将TensorBoard暴露到公网
3. **日志大小**: 长时间训练会产生大量日志文件，注意磁盘空间
4. **版本兼容**: 确保TensorBoard版本与TensorFlow兼容

## 相关文件

- `start_tensorboard.py`: TensorBoard启动脚本
- `logs/`: 训练日志目录
- `README_TRAINING_SETUP.md`: 训练设置指南
