# PlaNet cheetah_run 小规模训练设置指南

本文档介绍如何在 PlaNet 项目中进行 cheetah_run 任务的小规模训练，用于验证配置、checkpoint 和日志功能。

## 环境要求

- Python 3.7
- TensorFlow 1.13.1
- dm_control
- <PERSON><PERSON>o<PERSON><PERSON> (已配置 EGL 渲染)

## 快速开始

### 1. 使用预配置的训练脚本

我们提供了三个便捷的脚本：

#### 快速功能测试
```bash
python3 quick_test.py
```
测试所有核心功能，包括模块导入、环境创建、配置系统、模型创建和最小化训练。

#### 完整验证
```bash
python3 validate_training.py --test-resume --clean
```
进行完整的训练验证，包括配置保存、数据收集、checkpoint 创建和恢复训练测试。

#### 小规模训练启动器
```bash
# 查看可用配置
python3 start_small_training.py --show-configs

# 运行不同规模的训练
python3 start_small_training.py --scale tiny    # 2-3分钟
python3 start_small_training.py --scale small   # 10-15分钟  
python3 start_small_training.py --scale medium  # 30-60分钟
```

### 2. 手动训练命令

#### 基础训练
```bash
export MUJOCO_GL=egl
python3 -m planet.scripts.train \
    --logdir ./logs/cheetah_test \
    --config debug \
    --params "{'tasks': ['cheetah_run'], 'train_steps': 50, 'test_steps': 10, 'max_steps': 200}"
```

#### 恢复训练
```bash
export MUJOCO_GL=egl
python3 -m planet.scripts.train \
    --logdir ./logs/cheetah_test \
    --config debug \
    --resume_runs True \
    --params "{'train_steps': 30, 'test_steps': 5}"
```

## 训练规模配置

### Tiny (快速测试)
- 训练步数: 5
- 测试步数: 2
- 最大步数: 20
- 批次大小: [3, 8]
- 模型大小: 20
- 预计时间: 2-3分钟

### Small (基础训练)
- 训练步数: 50
- 测试步数: 10
- 最大步数: 200
- 批次大小: [10, 20]
- 模型大小: 50
- 预计时间: 10-15分钟

### Medium (完整测试)
- 训练步数: 200
- 测试步数: 20
- 最大步数: 1000
- 批次大小: [20, 30]
- 模型大小: 100
- 预计时间: 30-60分钟

## 输出文件结构

训练完成后，日志目录结构如下：
```
logs/
└── [训练名称]/
    └── 00001/
        ├── config.yaml              # 训练配置
        ├── DONE                     # 完成标记
        ├── model.ckpt-*             # Checkpoint文件
        ├── train/                   # 训练TensorBoard日志
        ├── test/                    # 测试TensorBoard日志
        ├── train_episodes/          # 训练episode数据
        │   └── *.npz
        └── test_episodes/           # 测试episode数据
            └── *.npz
```

## 验证检查项

训练完成后会自动验证以下项目：
- ✓ 配置文件保存 (config.yaml)
- ✓ 训练数据收集 (train_episodes/*.npz)
- ✓ 测试数据收集 (test_episodes/*.npz)
- ✓ TensorBoard日志 (train/, test/)
- ✓ Checkpoint文件 (model.ckpt-*)
- ✓ 训练完成标记 (DONE)
- ✓ 恢复训练功能

## 查看训练结果

### TensorBoard
```bash
tensorboard --logdir ./logs/[训练目录]
```

### Episode数据分析
```python
import numpy as np

# 加载episode数据
data = np.load('logs/[训练目录]/00001/train_episodes/[episode文件].npz')
print("Episode包含的键:", list(data.keys()))
print("图像形状:", data['image'].shape)
print("奖励:", data['reward'])
print("动作:", data['action'])
```

## 常见问题

### 1. OpenGL错误
确保设置环境变量：
```bash
export MUJOCO_GL=egl
```

### 2. 除零错误
已修复 `print_metrics_every` 参数的除零问题。

### 3. 内存不足
使用更小的批次大小：
```python
'batch_shape': [5, 10]  # 减少批次大小
```

### 4. 训练时间过长
使用 tiny 或 small 配置进行快速测试。

## 自定义配置

可以通过修改参数来自定义训练：

```python
custom_params = {
    'tasks': ['cheetah_run'],
    'train_steps': 100,           # 训练步数
    'test_steps': 20,             # 测试步数
    'max_steps': 500,             # 最大步数
    'num_seed_episodes': 2,       # 初始随机episode数
    'collect_every': 50,          # 数据收集频率
    'checkpoint_every': 25,       # checkpoint保存频率
    'model_size': 100,            # 模型大小
    'state_size': 20,             # 状态大小
    'batch_shape': [15, 25],      # 批次形状
    'planner_amount': 300,        # 规划器采样数
    'planner_iterations': 6,      # 规划器迭代数
}
```

## 下一步

1. 验证所有功能正常后，可以增加训练规模
2. 尝试其他任务（如 cartpole_balance, walker_walk）
3. 调整模型参数进行性能优化
4. 使用 TensorBoard 分析训练过程

## 脚本说明

- `quick_test.py`: 快速功能测试
- `validate_training.py`: 完整训练验证
- `start_small_training.py`: 小规模训练启动器
- `test_cheetah_small.py`: 详细的测试脚本

所有脚本都已配置好环境变量和错误处理，可以直接使用。
