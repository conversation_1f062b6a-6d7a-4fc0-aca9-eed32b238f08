#!/usr/bin/env python3
"""
快速测试脚本 - 验证 PlaNet 基本功能
"""

import os
import sys
import tempfile
import shutil

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试关键模块导入"""
    print("测试模块导入...")
    
    try:
        import tensorflow as tf
        print(f"✓ TensorFlow {tf.__version__}")
    except ImportError as e:
        print(f"✗ TensorFlow 导入失败: {e}")
        return False
    
    try:
        import dm_control
        print("✓ dm_control")
    except ImportError as e:
        print(f"✗ dm_control 导入失败: {e}")
        return False
    
    try:
        from planet import control, models, networks, training, tools
        print("✓ planet 核心模块")
    except ImportError as e:
        print(f"✗ planet 模块导入失败: {e}")
        return False
    
    try:
        from planet.scripts import configs, tasks, train
        print("✓ planet.scripts 模块")
    except ImportError as e:
        print(f"✗ planet.scripts 模块导入失败: {e}")
        return False
    
    return True

def test_environment():
    """测试环境创建"""
    print("\n测试环境创建...")
    
    try:
        from planet.scripts import tasks as tasks_lib
        from planet import tools
        
        # 创建基本配置
        config = tools.AttrDict()
        params = tools.AttrDict({'action_repeat': 4})
        
        # 创建 cheetah_run 任务
        task = tasks_lib.cheetah_run(config, params)
        print(f"✓ 任务创建成功: {task.name}")
        print(f"  最大长度: {task.max_length}")
        print(f"  状态组件: {task.state_components}")
        
        # 测试环境实例化
        env = task.env_ctor()
        print("✓ 环境实例化成功")
        
        # 测试环境基本操作
        obs = env.reset()
        print(f"✓ 环境重置成功，观察空间: {obs.keys()}")
        
        action = env.action_space.sample()
        obs, reward, done, info = env.step(action)
        print(f"✓ 环境步进成功，奖励: {reward}")
        
        env.close()
        return True
        
    except Exception as e:
        print(f"✗ 环境测试失败: {e}")
        return False

def test_config():
    """测试配置系统"""
    print("\n测试配置系统...")
    
    try:
        from planet.scripts import configs
        from planet import tools
        
        # 测试默认配置
        config = tools.AttrDict()
        params = tools.AttrDict({
            'tasks': ['cheetah_run'],
            'logdir': '/tmp/test'
        })
        
        config = configs.default(config, params)
        print("✓ 默认配置创建成功")
        print(f"  训练步数: {config.train_steps}")
        print(f"  测试步数: {config.test_steps}")
        print(f"  批次形状: {config.batch_shape}")
        
        # 测试debug配置
        config_debug = tools.AttrDict()
        config_debug = configs.debug(config_debug, params)
        print("✓ Debug配置创建成功")
        print(f"  训练步数: {config_debug.train_steps}")
        print(f"  批次形状: {config_debug.batch_shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ 配置测试失败: {e}")
        return False

def test_model_creation():
    """测试模型创建"""
    print("\n测试模型创建...")
    
    try:
        import tensorflow as tf
        from planet import models, networks, tools
        
        # 重置图
        tf.reset_default_graph()
        
        # 创建基本参数
        state_size = 10
        model_size = 20
        activation = tf.nn.relu
        
        # 测试RSSM模型
        rssm = models.RSSM(state_size, model_size, model_size, True, False, 1e-1, activation, 1)
        print("✓ RSSM模型创建成功")
        
        # 测试网络
        network = networks.conv_ha
        encoder = network.encoder
        decoder = network.decoder
        print("✓ 编码器/解码器网络创建成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 模型创建测试失败: {e}")
        return False

def run_minimal_training():
    """运行最小化训练测试"""
    print("\n运行最小化训练测试...")
    
    # 创建临时目录
    logdir = tempfile.mkdtemp(prefix='planet_minimal_')
    print(f"使用临时目录: {logdir}")
    
    try:
        # 使用命令行方式运行
        import subprocess
        
        cmd = [
            'python3', '-m', 'planet.scripts.train',
            '--logdir', logdir,
            '--config', 'debug',
            '--params', "{'tasks': ['cheetah_run'], 'train_steps': 5, 'test_steps': 2, 'max_steps': 20, 'num_seed_episodes': 1}"
        ]
        
        print("执行命令:", ' '.join(cmd))
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✓ 最小化训练成功完成")
            
            # 检查输出文件
            config_file = os.path.join(logdir, 'config.yaml')
            if os.path.exists(config_file):
                print("✓ 配置文件已保存")
            
            train_dir = os.path.join(logdir, 'train_episodes')
            if os.path.exists(train_dir):
                files = os.listdir(train_dir)
                print(f"✓ 训练数据目录存在，包含 {len(files)} 个文件")
            
            return True
        else:
            print(f"✗ 训练失败，返回码: {result.returncode}")
            print("错误输出:", result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("✗ 训练超时")
        return False
    except Exception as e:
        print(f"✗ 训练测试失败: {e}")
        return False
    finally:
        # 清理临时目录
        shutil.rmtree(logdir, ignore_errors=True)

def main():
    """主测试函数"""
    print("PlaNet 快速功能测试")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("环境创建", test_environment),
        ("配置系统", test_config),
        ("模型创建", test_model_creation),
        ("最小化训练", run_minimal_training),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n[{test_name}]")
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 50)
    print("测试结果总结:")
    passed = 0
    for test_name, success in results:
        status = "✓ 通过" if success else "✗ 失败"
        print(f"  {test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！系统配置正常。")
    else:
        print("⚠️  部分测试失败，请检查环境配置。")

if __name__ == '__main__':
    main()
